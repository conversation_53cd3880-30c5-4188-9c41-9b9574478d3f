import React, { useEffect } from 'react';
import { useAuthStore } from './stores/authStore';

console.log('App-debug.jsx 开始加载');

// 第二步：测试authStore
function AppDebug() {
  console.log('AppDebug 组件渲染');

  try {
    const { initAuth } = useAuthStore();
    console.log('authStore 获取成功');

    useEffect(() => {
      console.log('开始调用 initAuth');
      initAuth();
      console.log('initAuth 调用完成');
    }, [initAuth]);

  } catch (error) {
    console.error('authStore 错误:', error);
    return (
      <div style={{ padding: '20px', color: 'red' }}>
        <h1>❌ authStore 错误</h1>
        <p>{error.message}</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🔍 App调试版本 - 测试authStore</h1>
      <p>基础App组件正常工作</p>
      <p>authStore 导入和使用正常</p>
      <p>下一步：测试React Router</p>
    </div>
  );
}

console.log('App-debug.jsx 加载完成');

export default AppDebug;
