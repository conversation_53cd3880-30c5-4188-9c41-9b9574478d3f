﻿import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  DatePicker,
  Select,
  Space,
  Progress,
  Table,
  Tag,
  Button,
  message,
  Alert,
  Divider,
  List,
  Avatar,
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  UserOutlined,
  MessageOutlined,
  LikeOutlined,
  EyeOutlined,
  TrophyOutlined,
  FireOutlined,
  ReloadOutlined,
  DownloadOutlined,
  RiseOutlined,
  FallOutlined,
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const AnalyticsPage = () => {
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('7days');
  const [dataType, setDataType] = useState('overview');

  // 模拟统计数据
  const [stats, setStats] = useState({
    totalViews: 12580,
    totalLikes: 3420,
    totalComments: 856,
    totalFollowers: 1240,
    viewsGrowth: 12.5,
    likesGrowth: 8.3,
    commentsGrowth: -2.1,
    followersGrowth: 15.7,
  });

  // 模拟热门内容数据
  const [hotContent, setHotContent] = useState([
    {
      id: 1,
      title: '我的护肤心得分享',
      views: 2580,
      likes: 420,
      comments: 156,
      engagement_rate: 22.3,
      created_at: '2024-01-15',
    },
    {
      id: 2,
      title: '口红试色合集',
      views: 1890,
      likes: 380,
      comments: 98,
      engagement_rate: 25.3,
      created_at: '2024-01-14',
    },
    {
      id: 3,
      title: '今日穿搭分享',
      views: 1650,
      likes: 290,
      comments: 67,
      engagement_rate: 21.6,
      created_at: '2024-01-13',
    },
    {
      id: 4,
      title: '居家收纳小技巧',
      views: 1420,
      likes: 310,
      comments: 89,
      engagement_rate: 28.1,
      created_at: '2024-01-12',
    },
    {
      id: 5,
      title: '简单家常菜制作',
      views: 1280,
      likes: 250,
      comments: 78,
      engagement_rate: 25.6,
      created_at: '2024-01-11',
    },
  ]);

  // 模拟用户互动数据
  const [userInteractions, setUserInteractions] = useState([
    {
      user_name: '美妆达人小李',
      avatar: '',
      interactions: 15,
      last_interaction: '2024-01-15 14:30:00',
      type: 'active_fan',
    },
    {
      user_name: '时尚小达人',
      avatar: '',
      interactions: 12,
      last_interaction: '2024-01-15 13:45:00',
      type: 'potential_fan',
    },
    {
      user_name: '生活记录者',
      avatar: '',
      interactions: 10,
      last_interaction: '2024-01-15 12:20:00',
      type: 'active_fan',
    },
    {
      user_name: '美食爱好者',
      avatar: '',
      interactions: 8,
      last_interaction: '2024-01-15 11:15:00',
      type: 'new_fan',
    },
  ]);

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange, dataType]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('数据加载成功');
    } catch (error) {
      message.error('数据加载失败');
    } finally {
      setLoading(false);
    }
  };

  const getGrowthIcon = (growth) => {
    if (growth > 0) {
      return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
    } else if (growth < 0) {
      return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
    }
    return null;
  };

  const getGrowthColor = (growth) => {
    if (growth > 0) return '#52c41a';
    if (growth < 0) return '#ff4d4f';
    return '#666';
  };

  const contentColumns = [
    {
      title: '内容标题',
      dataIndex: 'title',
      key: 'title',
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: '浏览量',
      dataIndex: 'views',
      key: 'views',
      render: (views) => (
        <Space>
          <EyeOutlined />
          {views.toLocaleString()}
        </Space>
      ),
    },
    {
      title: '点赞数',
      dataIndex: 'likes',
      key: 'likes',
      render: (likes) => (
        <Space>
          <LikeOutlined />
          {likes.toLocaleString()}
        </Space>
      ),
    },
    {
      title: '评论数',
      dataIndex: 'comments',
      key: 'comments',
      render: (comments) => (
        <Space>
          <MessageOutlined />
          {comments.toLocaleString()}
        </Space>
      ),
    },
    {
      title: '互动率',
      dataIndex: 'engagement_rate',
      key: 'engagement_rate',
      render: (rate) => (
        <Tag color={rate > 25 ? 'green' : rate > 20 ? 'orange' : 'default'}>
          {rate}%
        </Tag>
      ),
    },
    {
      title: '发布时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => <Text type="secondary">{date}</Text>,
    },
  ];

  const getUserTypeTag = (type) => {
    switch (type) {
      case 'active_fan':
        return <Tag color="green">活跃粉丝</Tag>;
      case 'potential_fan':
        return <Tag color="orange">潜在粉丝</Tag>;
      case 'new_fan':
        return <Tag color="blue">新粉丝</Tag>;
      default:
        return <Tag>普通用户</Tag>;
    }
  };

  return (
    <div className="analytics-page">
      <div className="page-header" style={{ marginBottom: 24 }}>
        <Title level={2}>数据分析</Title>
        <Paragraph type="secondary">
          分析账号数据表现，了解内容效果，优化运营策略
        </Paragraph>
      </div>

      {/* 时间筛选和操作 */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Select
                value={timeRange}
                onChange={setTimeRange}
                style={{ width: 120 }}
              >
                <Option value="1day">今日</Option>
                <Option value="7days">近7天</Option>
                <Option value="30days">近30天</Option>
                <Option value="90days">近90天</Option>
              </Select>
              <Select
                value={dataType}
                onChange={setDataType}
                style={{ width: 120 }}
              >
                <Option value="overview">概览</Option>
                <Option value="content">内容分析</Option>
                <Option value="user">用户分析</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadAnalyticsData}
                loading={loading}
              >
                刷新数据
              </Button>
              <Button
                icon={<DownloadOutlined />}
                type="primary"
              >
                导出报告
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 核心指标统计 */}
      <Row gutter={24} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总浏览量"
              value={stats.totalViews}
              prefix={<EyeOutlined />}
              suffix={
                <span style={{ fontSize: '14px', color: getGrowthColor(stats.viewsGrowth) }}>
                  {getGrowthIcon(stats.viewsGrowth)}
                  {Math.abs(stats.viewsGrowth)}%
                </span>
              }
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总点赞数"
              value={stats.totalLikes}
              prefix={<LikeOutlined />}
              suffix={
                <span style={{ fontSize: '14px', color: getGrowthColor(stats.likesGrowth) }}>
                  {getGrowthIcon(stats.likesGrowth)}
                  {Math.abs(stats.likesGrowth)}%
                </span>
              }
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总评论数"
              value={stats.totalComments}
              prefix={<MessageOutlined />}
              suffix={
                <span style={{ fontSize: '14px', color: getGrowthColor(stats.commentsGrowth) }}>
                  {getGrowthIcon(stats.commentsGrowth)}
                  {Math.abs(stats.commentsGrowth)}%
                </span>
              }
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="粉丝数量"
              value={stats.totalFollowers}
              prefix={<UserOutlined />}
              suffix={
                <span style={{ fontSize: '14px', color: getGrowthColor(stats.followersGrowth) }}>
                  {getGrowthIcon(stats.followersGrowth)}
                  {Math.abs(stats.followersGrowth)}%
                </span>
              }
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据趋势 */}
      <Row gutter={24} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="内容表现趋势" extra={<FireOutlined />}>
            <div style={{ marginBottom: 16 }}>
              <Text>平均互动率</Text>
              <Progress
                percent={23.5}
                status="active"
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
            </div>
            <div style={{ marginBottom: 16 }}>
              <Text>内容质量评分</Text>
              <Progress
                percent={78}
                status="active"
                strokeColor="#52c41a"
              />
            </div>
            <div>
              <Text>粉丝活跃度</Text>
              <Progress
                percent={65}
                status="active"
                strokeColor="#fa8c16"
              />
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="增长指标" extra={<RiseOutlined />}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="日均新增粉丝"
                  value={18}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<UserOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="内容发布频率"
                  value={2.3}
                  precision={1}
                  suffix="篇/天"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            </Row>
            <Divider />
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="平均互动时间"
                  value={3.2}
                  precision={1}
                  suffix="分钟"
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="回复响应率"
                  value={89}
                  suffix="%"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 热门内容和用户互动 */}
      <Row gutter={24}>
        <Col span={16}>
          <Card title="热门内容排行" extra={<TrophyOutlined />}>
            <Table
              columns={contentColumns}
              dataSource={hotContent}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="活跃用户" extra={<UserOutlined />}>
            <List
              dataSource={userInteractions}
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title={
                      <Space>
                        <Text strong>{item.user_name}</Text>
                        {getUserTypeTag(item.type)}
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary">互动 {item.interactions} 次</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {item.last_interaction}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据洞察 */}
      <Card title="数据洞察" style={{ marginTop: 24 }}>
        <Row gutter={24}>
          <Col span={8}>
            <Alert
              message="内容表现优秀"
              description="本周内容互动率较上周提升12.5%，用户参与度显著提高。"
              type="success"
              showIcon
            />
          </Col>
          <Col span={8}>
            <Alert
              message="粉丝增长稳定"
              description="粉丝增长保持稳定趋势，建议继续保持内容质量。"
              type="info"
              showIcon
            />
          </Col>
          <Col span={8}>
            <Alert
              message="回复及时性待提升"
              description="部分留言回复时间较长，建议提高响应速度。"
              type="warning"
              showIcon
            />
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default AnalyticsPage;
