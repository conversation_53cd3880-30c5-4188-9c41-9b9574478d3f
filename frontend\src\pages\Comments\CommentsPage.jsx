import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  Avatar,
} from 'antd';
import {
  MessageOutlined,
  ReplyOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
  SendOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FilterOutlined,
  RobotOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search, TextArea } = Input;

const CommentsPage = () => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [selectedComment, setSelectedComment] = useState(null);
  const [replyForm] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    loadComments();
  }, []);

  const loadComments = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const mockComments = [
        {
          id: 1,
          content: '这个护肤品真的很好用！',
          author: '小红用户123',
          avatar: null,
          noteTitle: '夏日护肤心得',
          createTime: '2024-01-18 10:30:00',
          status: 'pending',
          replies: [],
        },
        {
          id: 2,
          content: '请问在哪里可以买到？',
          author: '时尚达人',
          avatar: null,
          noteTitle: '美妆分享',
          createTime: '2024-01-18 09:45:00',
          status: 'replied',
          replies: [
            {
              content: '可以在官方旗舰店购买哦～',
              createTime: '2024-01-18 10:00:00',
              type: 'auto'
            }
          ],
        },
        {
          id: 3,
          content: '效果怎么样？值得购买吗？',
          author: '美妆新手',
          avatar: null,
          noteTitle: '护肤心得',
          createTime: '2024-01-18 08:20:00',
          status: 'ignored',
          replies: [],
        },
      ];
      setComments(mockComments);
    } catch (error) {
      message.error('加载留言失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReply = (comment) => {
    setSelectedComment(comment);
    setReplyModalVisible(true);
    replyForm.resetFields();
  };

  const handleSendReply = async (values) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedComments = comments.map(comment => {
        if (comment.id === selectedComment.id) {
          return {
            ...comment,
            status: 'replied',
            replies: [
              ...comment.replies,
              {
                content: values.replyContent,
                createTime: new Date().toLocaleString(),
                type: 'manual'
              }
            ]
          };
        }
        return comment;
      });
      
      setComments(updatedComments);
      setReplyModalVisible(false);
      message.success('回复发送成功');
    } catch (error) {
      message.error('回复发送失败');
    }
  };

  const handleIgnore = async (commentId) => {
    try {
      const updatedComments = comments.map(comment => {
        if (comment.id === commentId) {
          return { ...comment, status: 'ignored' };
        }
        return comment;
      });
      setComments(updatedComments);
      message.success('已忽略该留言');
    } catch (error) {
      message.error('操作失败');
    }
  };

  const getStatusTag = (status) => {
    const statusMap = {
      pending: { color: 'orange', text: '待回复' },
      replied: { color: 'green', text: '已回复' },
      ignored: { color: 'gray', text: '已忽略' },
    };
    const config = statusMap[status] || statusMap.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const filteredComments = comments.filter(comment => {
    const matchesSearch = comment.content.toLowerCase().includes(searchText.toLowerCase()) ||
                         comment.author.toLowerCase().includes(searchText.toLowerCase()) ||
                         comment.noteTitle.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || comment.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const columns = [
    {
      title: '用户',
      dataIndex: 'author',
      key: 'author',
      width: 120,
      render: (author, record) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} src={record.avatar} />
          <Text>{author}</Text>
        </Space>
      ),
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (content) => (
        <Text style={{ maxWidth: 300 }}>{content}</Text>
      ),
    },
    {
      title: '所属笔记',
      dataIndex: 'noteTitle',
      key: 'noteTitle',
      width: 150,
      ellipsis: true,
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      render: (time) => (
        <Space>
          <ClockCircleOutlined />
          <Text>{time}</Text>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<ReplyOutlined />}
            onClick={() => handleReply(record)}
            disabled={record.status === 'replied'}
          >
            回复
          </Button>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              Modal.info({
                title: '留言详情',
                content: (
                  <div>
                    <p><strong>用户：</strong>{record.author}</p>
                    <p><strong>内容：</strong>{record.content}</p>
                    <p><strong>笔记：</strong>{record.noteTitle}</p>
                    <p><strong>时间：</strong>{record.createTime}</p>
                    {record.replies.length > 0 && (
                      <div>
                        <strong>回复记录：</strong>
                        {record.replies.map((reply, index) => (
                          <div key={index} style={{ marginTop: 8, padding: 8, background: '#f5f5f5' }}>
                            <p>{reply.content}</p>
                            <small>{reply.createTime} ({reply.type === 'auto' ? '自动回复' : '手动回复'})</small>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ),
                width: 600,
              });
            }}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <Button
              size="small"
              icon={<CloseOutlined />}
              onClick={() => handleIgnore(record.id)}
            >
              忽略
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const stats = {
    total: comments.length,
    pending: comments.filter(c => c.status === 'pending').length,
    replied: comments.filter(c => c.status === 'replied').length,
    ignored: comments.filter(c => c.status === 'ignored').length,
  };

  return (
    <div className="comments-page">
      <div className="page-header">
        <Title level={2}>留言管理</Title>
        <Text type="secondary">管理和回复用户留言，提升用户互动体验</Text>
      </div>

      <Row gutter={24} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总留言数"
              value={stats.total}
              prefix={<MessageOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待回复"
              value={stats.pending}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已回复"
              value={stats.replied}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已忽略"
              value={stats.ignored}
              valueStyle={{ color: '#8c8c8c' }}
              prefix={<CloseOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Search
                placeholder="搜索留言内容、用户或笔记"
                allowClear
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                prefix={<SearchOutlined />}
              />
            </Col>
            <Col span={4}>
              <Select
                style={{ width: '100%' }}
                value={statusFilter}
                onChange={setStatusFilter}
                prefix={<FilterOutlined />}
              >
                <Option value="all">全部状态</Option>
                <Option value="pending">待回复</Option>
                <Option value="replied">已回复</Option>
                <Option value="ignored">已忽略</Option>
              </Select>
            </Col>
            <Col span={4}>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadComments}
                loading={loading}
              >
                刷新
              </Button>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={filteredComments}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredComments.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条留言`,
          }}
        />
      </Card>

      <Modal
        title="回复留言"
        open={replyModalVisible}
        onCancel={() => setReplyModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedComment && (
          <div>
            <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
              <p><strong>用户：</strong>{selectedComment.author}</p>
              <p><strong>留言：</strong>{selectedComment.content}</p>
              <p><strong>笔记：</strong>{selectedComment.noteTitle}</p>
            </div>
            
            <Form
              form={replyForm}
              onFinish={handleSendReply}
              layout="vertical"
            >
              <Form.Item
                label="回复内容"
                name="replyContent"
                rules={[{ required: true, message: '请输入回复内容' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入回复内容..."
                  showCount
                  maxLength={500}
                />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SendOutlined />}
                  >
                    发送回复
                  </Button>
                  <Button onClick={() => setReplyModalVisible(false)}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CommentsPage;
