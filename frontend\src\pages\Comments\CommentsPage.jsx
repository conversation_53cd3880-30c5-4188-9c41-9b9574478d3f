﻿import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip,
  Popconfirm,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  MessageOutlined,
  CheckOutlined,
  CloseOutlined,
  RobotOutlined,
  EyeOutlined,
  DeleteOutlined,
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CommentsPage = () => {
  const [loading, setLoading] = useState(false);
  const [comments, setComments] = useState([]);
  const [filteredComments, setFilteredComments] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [selectedComment, setSelectedComment] = useState(null);
  const [replyForm] = Form.useForm();

  // 模拟数据
  const mockComments = [
    {
      id: 1,
      user_name: '小红薯用户1',
      user_avatar: '',
      content: '这个产品真的很好用，推荐给大家！',
      note_title: '我的护肤心得分享',
      created_at: '2024-01-15 14:30:00',
      status: 'pending',
      reply_content: null,
      reply_time: null,
      likes: 12,
      platform: 'xiaohongshu'
    },
    {
      id: 2,
      user_name: '美妆达人小李',
      user_avatar: '',
      content: '请问这个色号适合黄皮吗？',
      note_title: '口红试色合集',
      created_at: '2024-01-15 13:45:00',
      status: 'replied',
      reply_content: '这个色号很适合黄皮哦，显白效果很好！',
      reply_time: '2024-01-15 14:00:00',
      likes: 8,
      platform: 'xiaohongshu'
    },
    {
      id: 3,
      user_name: '时尚小达人',
      user_avatar: '',
      content: '哪里可以买到同款？',
      note_title: '今日穿搭分享',
      created_at: '2024-01-15 12:20:00',
      status: 'pending',
      reply_content: null,
      reply_time: null,
      likes: 5,
      platform: 'xiaohongshu'
    },
    {
      id: 4,
      user_name: '生活记录者',
      user_avatar: '',
      content: '太实用了，收藏了！',
      note_title: '居家收纳小技巧',
      created_at: '2024-01-15 11:15:00',
      status: 'replied',
      reply_content: '谢谢支持！还有更多小技巧会持续分享的～',
      reply_time: '2024-01-15 11:30:00',
      likes: 15,
      platform: 'xiaohongshu'
    },
    {
      id: 5,
      user_name: '美食爱好者',
      user_avatar: '',
      content: '看起来好好吃，有详细做法吗？',
      note_title: '简单家常菜制作',
      created_at: '2024-01-15 10:30:00',
      status: 'pending',
      reply_content: null,
      reply_time: null,
      likes: 20,
      platform: 'xiaohongshu'
    }
  ];

  const [stats] = useState({
    total: 156,
    pending: 23,
    replied: 133,
    todayNew: 8
  });

  useEffect(() => {
    loadComments();
  }, []);

  useEffect(() => {
    filterComments();
  }, [comments, searchText, statusFilter]);

  const loadComments = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setComments(mockComments);
      message.success('留言数据加载成功');
    } catch (error) {
      message.error('加载留言失败');
    } finally {
      setLoading(false);
    }
  };

  const filterComments = () => {
    let filtered = comments;

    // 状态筛选
    if (statusFilter !== 'all') {
      filtered = filtered.filter(comment => comment.status === statusFilter);
    }

    // 搜索筛选
    if (searchText) {
      filtered = filtered.filter(comment =>
        comment.content.toLowerCase().includes(searchText.toLowerCase()) ||
        comment.user_name.toLowerCase().includes(searchText.toLowerCase()) ||
        comment.note_title.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    setFilteredComments(filtered);
  };

  const handleReply = (comment) => {
    setSelectedComment(comment);
    setReplyModalVisible(true);
    replyForm.resetFields();
  };

  const handleSubmitReply = async (values) => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 更新评论状态
      const updatedComments = comments.map(comment => {
        if (comment.id === selectedComment.id) {
          return {
            ...comment,
            status: 'replied',
            reply_content: values.reply_content,
            reply_time: new Date().toLocaleString()
          };
        }
        return comment;
      });
      
      setComments(updatedComments);
      setReplyModalVisible(false);
      message.success('回复发送成功');
    } catch (error) {
      message.error('回复发送失败');
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsReplied = async (commentId) => {
    try {
      const updatedComments = comments.map(comment => {
        if (comment.id === commentId) {
          return {
            ...comment,
            status: 'replied',
            reply_content: '已处理',
            reply_time: new Date().toLocaleString()
          };
        }
        return comment;
      });
      
      setComments(updatedComments);
      message.success('标记为已回复');
    } catch (error) {
      message.error('操作失败');
    }
  };

  const handleDelete = async (commentId) => {
    try {
      const updatedComments = comments.filter(comment => comment.id !== commentId);
      setComments(updatedComments);
      message.success('留言删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const getStatusTag = (status) => {
    switch (status) {
      case 'pending':
        return <Tag color="orange">待回复</Tag>;
      case 'replied':
        return <Tag color="green">已回复</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const columns = [
    {
      title: '用户',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 120,
      render: (text) => (
        <div>
          <Text strong>{text}</Text>
        </div>
      ),
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (text, record) => (
        <div>
          <Paragraph ellipsis={{ rows: 2, expandable: true }}>
            {text}
          </Paragraph>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            来自: {record.note_title}
          </Text>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status),
    },
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (text) => (
        <Text type="secondary">{text}</Text>
      ),
    },
    {
      title: '点赞',
      dataIndex: 'likes',
      key: 'likes',
      width: 80,
      render: (likes) => (
        <Badge count={likes} style={{ backgroundColor: '#52c41a' }} />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          {record.status === 'pending' && (
            <>
              <Tooltip title="回复">
                <Button
                  type="primary"
                  size="small"
                  icon={<MessageOutlined />}
                  onClick={() => handleReply(record)}
                />
              </Tooltip>
              <Tooltip title="标记已回复">
                <Button
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={() => handleMarkAsReplied(record.id)}
                />
              </Tooltip>
            </>
          )}
          {record.status === 'replied' && (
            <Tooltip title="查看回复">
              <Button
                size="small"
                icon={<EyeOutlined />}
                onClick={() => {
                  Modal.info({
                    title: '回复内容',
                    content: (
                      <div>
                        <p><strong>原留言：</strong>{record.content}</p>
                        <p><strong>回复内容：</strong>{record.reply_content}</p>
                        <p><strong>回复时间：</strong>{record.reply_time}</p>
                      </div>
                    ),
                  });
                }}
              />
            </Tooltip>
          )}
          <Popconfirm
            title="确定删除这条留言吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="comments-page">
      <div className="page-header" style={{ marginBottom: 24 }}>
        <Title level={2}>留言管理</Title>
        <Paragraph type="secondary">
          管理用户留言，及时回复用户问题，提升用户体验
        </Paragraph>
      </div>

      {/* 统计卡片 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={24}>
          <Col span={6}>
            <Statistic
              title="总留言数"
              value={stats.total}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="待回复"
              value={stats.pending}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已回复"
              value={stats.replied}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="今日新增"
              value={stats.todayNew}
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Input
              placeholder="搜索留言内容、用户名或笔记标题"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态筛选"
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: '100%' }}
            >
              <Option value="all">全部状态</Option>
              <Option value="pending">待回复</Option>
              <Option value="replied">已回复</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadComments}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 留言列表 */}
      <Card title="留言列表">
        <Table
          columns={columns}
          dataSource={filteredComments}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredComments.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 第 - 条，共  条,
          }}
        />
      </Card>

      {/* 回复弹窗 */}
      <Modal
        title="回复留言"
        open={replyModalVisible}
        onCancel={() => setReplyModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedComment && (
          <div>
            <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
              <Text strong>原留言：</Text>
              <div style={{ marginTop: 8 }}>
                <Text>{selectedComment.content}</Text>
              </div>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">
                  来自：{selectedComment.user_name} | {selectedComment.created_at}
                </Text>
              </div>
            </div>
            
            <Form
              form={replyForm}
              onFinish={handleSubmitReply}
              layout="vertical"
            >
              <Form.Item
                label="回复内容"
                name="reply_content"
                rules={[{ required: true, message: '请输入回复内容' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入回复内容..."
                  showCount
                  maxLength={500}
                />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<MessageOutlined />}
                  >
                    发送回复
                  </Button>
                  <Button
                    icon={<RobotOutlined />}
                    onClick={() => {
                      // 模拟AI生成回复
                      const aiReply = 感谢您的留言！针对您的问题，我来为您详细解答...;
                      replyForm.setFieldsValue({ reply_content: aiReply });
                      message.success('AI回复已生成');
                    }}
                  >
                    AI生成回复
                  </Button>
                  <Button onClick={() => setReplyModalVisible(false)}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CommentsPage;
