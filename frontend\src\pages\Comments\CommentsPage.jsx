import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  Tooltip,
  Avatar,
} from 'antd';
import {
  MessageOutlined,
  ReplyOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
  SendOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FilterOutlined,
  RobotOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search, TextArea } = Input;

const CommentsPage = () => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [selectedComment, setSelectedComment] = useState(null);
  const [replyForm] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    loadComments();
  }, []);

  const loadComments = async () => {
    setLoading(true);
    try {
      // 模拟加载留言数据
      await new Promise(resolve => setTimeout(resolve, 1000));
      const mockComments = [
        {
          id: 1,
          note_title: '美妆分享 - 夏日护肤心得',
          user_name: '小美同学',
          user_avatar: '',
          content: '这个护肤品真的很好用，推荐给大家！',
          created_time: '2024-01-18 10:30:00',
          reply_status: 'pending',
          sentiment: 'positive',
          keywords: ['护肤品', '推荐'],
        },
        {
          id: 2,
          note_title: '美妆分享 - 夏日护肤心得',
          user_name: '爱美小仙女',
          user_avatar: '',
          content: '请问这个产品适合敏感肌吗？',
          created_time: '2024-01-18 11:15:00',
          reply_status: 'replied',
          reply_content: '这个产品比较温和，敏感肌也可以使用，建议先在手腕测试一下。',
          reply_time: '2024-01-18 11:30:00',
          sentiment: 'neutral',
          keywords: ['敏感肌', '产品'],
        },
        {
          id: 3,
          note_title: '时尚穿搭 - 春季搭配指南',
          user_name: '时尚达人',
          user_avatar: '',
          content: '这套搭配太好看了！',
          created_time: '2024-01-18 12:00:00',
          reply_status: 'ignored',
          sentiment: 'positive',
          keywords: ['搭配', '好看'],
        },
      ];
      setComments(mockComments);
    } catch (error) {
      message.error('加载留言失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReply = (comment) => {
    setSelectedComment(comment);
    setReplyModalVisible(true);
    replyForm.resetFields();
  };

  const handleReplySubmit = async (values) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('回复成功');
      setReplyModalVisible(false);
      loadComments();
    } catch (error) {
      message.error('回复失败');
    }
  };

  const handleIgnore = async (commentId) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success('已忽略该留言');
      loadComments();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const getStatusTag = (status) => {
    const statusMap = {
      pending: { color: 'orange', text: '待回复' },
      replied: { color: 'green', text: '已回复' },
      ignored: { color: 'gray', text: '已忽略' },
    };
    const config = statusMap[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getSentimentTag = (sentiment) => {
    const sentimentMap = {
      positive: { color: 'green', text: '正面' },
      negative: { color: 'red', text: '负面' },
      neutral: { color: 'blue', text: '中性' },
    };
    const config = sentimentMap[sentiment] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: '用户',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 120,
      render: (text, record) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} src={record.user_avatar} />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '所属笔记',
      dataIndex: 'note_title',
      key: 'note_title',
      width: 200,
      ellipsis: true,
    },
    {
      title: '情感分析',
      dataIndex: 'sentiment',
      key: 'sentiment',
      width: 100,
      render: (sentiment) => getSentimentTag(sentiment),
    },
    {
      title: '状态',
      dataIndex: 'reply_status',
      key: 'reply_status',
      width: 100,
      render: (status) => getStatusTag(status),
    },
    {
      title: '时间',
      dataIndex: 'created_time',
      key: 'created_time',
      width: 150,
      render: (time) => (
        <Space direction="vertical" size={0}>
          <span>{time.split(' ')[0]}</span>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {time.split(' ')[1]}
          </Text>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              size="small"
            />
          </Tooltip>
          {record.reply_status === 'pending' && (
            <>
              <Tooltip title="回复">
                <Button
                  type="link"
                  icon={<ReplyOutlined />}
                  size="small"
                  onClick={() => handleReply(record)}
                />
              </Tooltip>
              <Tooltip title="忽略">
                <Button
                  type="link"
                  icon={<CloseOutlined />}
                  size="small"
                  onClick={() => handleIgnore(record.id)}
                />
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ];

  const filteredComments = comments.filter(comment => {
    const matchesSearch = !searchText || 
      comment.content.toLowerCase().includes(searchText.toLowerCase()) ||
      comment.user_name.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || comment.reply_status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="comments-page">
      <div className="page-header">
        <Title level={2}>留言管理</Title>
        <Text type="secondary">管理和回复小红书笔记的用户留言</Text>
      </div>

      <Row gutter={24} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总留言数"
              value={comments.length}
              prefix={<MessageOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待回复"
              value={comments.filter(c => c.reply_status === 'pending').length}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已回复"
              value={comments.filter(c => c.reply_status === 'replied').length}
              prefix={<CheckOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已忽略"
              value={comments.filter(c => c.reply_status === 'ignored').length}
              prefix={<CloseOutlined />}
              valueStyle={{ color: '#8c8c8c' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Search
              placeholder="搜索留言内容或用户名"
              allowClear
              style={{ width: 300 }}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
            <Select
              placeholder="筛选状态"
              style={{ width: 120 }}
              value={statusFilter}
              onChange={setStatusFilter}
            >
              <Option value="all">全部状态</Option>
              <Option value="pending">待回复</Option>
              <Option value="replied">已回复</Option>
              <Option value="ignored">已忽略</Option>
            </Select>
            <Button icon={<ReloadOutlined />} onClick={loadComments}>
              刷新
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredComments}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredComments.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title="回复留言"
        open={replyModalVisible}
        onCancel={() => setReplyModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedComment && (
          <div>
            <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
              <div><strong>用户：</strong>{selectedComment.user_name}</div>
              <div><strong>留言：</strong>{selectedComment.content}</div>
              <div><strong>时间：</strong>{selectedComment.created_time}</div>
            </div>
            
            <Form form={replyForm} onFinish={handleReplySubmit} layout="vertical">
              <Form.Item
                label="回复内容"
                name="content"
                rules={[{ required: true, message: '请输入回复内容' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入回复内容..."
                  showCount
                  maxLength={500}
                />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SendOutlined />}>
                    发送回复
                  </Button>
                  <Button onClick={() => setReplyModalVisible(false)}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CommentsPage;
