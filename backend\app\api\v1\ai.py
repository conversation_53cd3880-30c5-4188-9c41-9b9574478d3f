from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..deps import get_current_user, get_db
from ...models.user import User
from ...models.reply_template import Reply<PERSON><PERSON><PERSON>, AIReply, AIRule, AIConfig
from ...services.ai_service import AIService
from ...services.ai_provider_manager import AIProviderManager, AIProvider
from ...services.ollama_service import get_ollama_service
from ...api.responses import success_response, error_response

router = APIRouter()


# Pydantic模型
class GenerateReplyRequest(BaseModel):
    comment_content: str
    context: Optional[dict] = None
    template_id: Optional[int] = None


class BatchGenerateRequest(BaseModel):
    comments: List[dict]


class ReplyTemplateCreate(BaseModel):
    name: str
    category: str
    content: str
    variables: Optional[dict] = None
    tags: Optional[List[str]] = None


class ReplyTemplateUpdate(BaseModel):
    name: Optional[str] = None
    category: Optional[str] = None
    content: Optional[str] = None
    variables: Optional[dict] = None
    tags: Optional[List[str]] = None
    is_active: Optional[bool] = None


class AIRuleCreate(BaseModel):
    name: str
    description: Optional[str] = None
    keywords: Optional[List[str]] = None
    template_ids: Optional[List[int]] = None
    ai_prompt: Optional[str] = None
    reply_style: Optional[str] = None
    priority: Optional[int] = 0
    auto_reply: Optional[bool] = False
    require_approval: Optional[bool] = True
    daily_limit: Optional[int] = None


class AIConfigUpdate(BaseModel):
    default_model: Optional[str] = None
    temperature: Optional[str] = None
    max_tokens: Optional[int] = None
    reply_language: Optional[str] = None
    reply_tone: Optional[str] = None
    include_emoji: Optional[bool] = None
    content_filter: Optional[bool] = None
    max_daily_requests: Optional[int] = None


# AI回复生成
@router.post("/generate-reply")
async def generate_reply(
    request: GenerateReplyRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """生成AI回复"""
    try:
        ai_service = AIService(db, current_user.id)
        result = await ai_service.generate_reply(
            comment_content=request.comment_content,
            context=request.context,
            template_id=request.template_id
        )
        
        if result["success"]:
            return success_response(
                data=result,
                message="AI回复生成成功"
            )
        else:
            return error_response(
                message=result["error"],
                status_code=status.HTTP_400_BAD_REQUEST
            )
    except Exception as e:
        return error_response(
            message=f"生成AI回复失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/generate-suggestions")
async def generate_suggestions(
    request: GenerateReplyRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取回复建议"""
    try:
        ai_service = AIService(db, current_user.id)
        suggestions = await ai_service.get_reply_suggestions(
            comment_content=request.comment_content,
            limit=3
        )
        
        return success_response(
            data={"suggestions": suggestions},
            message="回复建议获取成功"
        )
    except Exception as e:
        return error_response(
            message=f"获取回复建议失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/batch-generate")
async def batch_generate_replies(
    request: BatchGenerateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量生成回复"""
    try:
        ai_service = AIService(db, current_user.id)
        results = await ai_service.batch_generate_replies(request.comments)
        
        return success_response(
            data={"results": results},
            message="批量生成完成"
        )
    except Exception as e:
        return error_response(
            message=f"批量生成失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# 回复模板管理
@router.post("/templates")
def create_template(
    template: ReplyTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建回复模板"""
    try:
        db_template = ReplyTemplate(
            **template.dict(),
            user_id=current_user.id
        )
        db.add(db_template)
        db.commit()
        db.refresh(db_template)
        
        return success_response(
            data=db_template,
            message="模板创建成功"
        )
    except Exception as e:
        return error_response(
            message=f"创建模板失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/templates")
def get_templates(
    category: Optional[str] = None,
    is_active: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取回复模板列表"""
    query = db.query(ReplyTemplate).filter(ReplyTemplate.user_id == current_user.id)
    
    if category:
        query = query.filter(ReplyTemplate.category == category)
    if is_active is not None:
        query = query.filter(ReplyTemplate.is_active == is_active)
    
    templates = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return success_response(
        data={
            "templates": templates,
            "total": total,
            "skip": skip,
            "limit": limit
        },
        message="获取模板列表成功"
    )


@router.get("/templates/{template_id}")
def get_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取模板详情"""
    template = db.query(ReplyTemplate).filter(
        ReplyTemplate.id == template_id,
        ReplyTemplate.user_id == current_user.id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在"
        )
    
    return success_response(data=template, message="获取模板详情成功")


@router.put("/templates/{template_id}")
def update_template(
    template_id: int,
    template_update: ReplyTemplateUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新回复模板"""
    template = db.query(ReplyTemplate).filter(
        ReplyTemplate.id == template_id,
        ReplyTemplate.user_id == current_user.id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在"
        )
    
    update_data = template_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(template, field, value)
    
    db.commit()
    db.refresh(template)
    
    return success_response(data=template, message="模板更新成功")


@router.delete("/templates/{template_id}")
def delete_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除回复模板"""
    template = db.query(ReplyTemplate).filter(
        ReplyTemplate.id == template_id,
        ReplyTemplate.user_id == current_user.id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在"
        )
    
    db.delete(template)
    db.commit()
    
    return success_response(message="模板删除成功")


# AI规则管理
@router.post("/rules")
def create_rule(
    rule: AIRuleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建AI规则"""
    try:
        db_rule = AIRule(
            **rule.dict(),
            user_id=current_user.id
        )
        db.add(db_rule)
        db.commit()
        db.refresh(db_rule)
        
        return success_response(
            data=db_rule,
            message="规则创建成功"
        )
    except Exception as e:
        return error_response(
            message=f"创建规则失败: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/rules")
def get_rules(
    is_active: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取AI规则列表"""
    query = db.query(AIRule).filter(AIRule.user_id == current_user.id)
    
    if is_active is not None:
        query = query.filter(AIRule.is_active == is_active)
    
    rules = query.order_by(AIRule.priority.desc()).offset(skip).limit(limit).all()
    total = query.count()
    
    return success_response(
        data={
            "rules": rules,
            "total": total,
            "skip": skip,
            "limit": limit
        },
        message="获取规则列表成功"
    )


# AI配置管理
@router.get("/config")
def get_ai_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取AI配置"""
    config = db.query(AIConfig).filter(AIConfig.user_id == current_user.id).first()
    
    if not config:
        # 创建默认配置
        config = AIConfig(user_id=current_user.id)
        db.add(config)
        db.commit()
        db.refresh(config)
    
    # 隐藏敏感信息
    config_dict = config.__dict__.copy()
    if config_dict.get("api_key"):
        config_dict["api_key"] = "***" + config_dict["api_key"][-4:]
    
    return success_response(data=config_dict, message="获取AI配置成功")


@router.put("/config")
def update_ai_config(
    config_update: AIConfigUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新AI配置"""
    config = db.query(AIConfig).filter(AIConfig.user_id == current_user.id).first()
    
    if not config:
        config = AIConfig(user_id=current_user.id)
        db.add(config)
    
    update_data = config_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(config, field, value)
    
    db.commit()
    db.refresh(config)
    
    return success_response(data=config, message="AI配置更新成功")


# AI回复历史
@router.get("/replies")
def get_ai_replies(
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取AI回复历史"""
    query = db.query(AIReply).filter(AIReply.user_id == current_user.id)
    
    if status:
        query = query.filter(AIReply.status == status)
    
    replies = query.order_by(AIReply.created_at.desc()).offset(skip).limit(limit).all()
    total = query.count()
    
    return success_response(
        data={
            "replies": replies,
            "total": total,
            "skip": skip,
            "limit": limit
        },
        message="获取AI回复历史成功"
    )


@router.put("/replies/{reply_id}/feedback")
def update_reply_feedback(
    reply_id: int,
    feedback: str,  # good/bad/neutral
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新回复反馈"""
    reply = db.query(AIReply).filter(
        AIReply.id == reply_id,
        AIReply.user_id == current_user.id
    ).first()
    
    if not reply:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="回复记录不存在"
        )
    
    reply.user_feedback = feedback
    db.commit()
    
    return success_response(message="反馈更新成功")


# Ollama相关端点
@router.get("/providers")
async def get_ai_providers():
    """获取支持的AI提供商列表"""
    return success_response(data={
        "providers": [
            {
                "id": "ollama",
                "name": "Ollama",
                "description": "本地部署的开源大语言模型",
                "features": ["免费", "本地部署", "隐私保护", "多模型支持"],
                "requirements": ["需要本地安装Ollama服务"]
            },
            {
                "id": "openai",
                "name": "OpenAI",
                "description": "OpenAI官方API服务",
                "features": ["高质量", "稳定可靠", "多模型支持"],
                "requirements": ["需要API密钥", "按使用量付费"]
            },
            {
                "id": "anthropic",
                "name": "Anthropic Claude",
                "description": "Anthropic的Claude模型",
                "features": ["安全可靠", "长上下文", "高质量回复"],
                "requirements": ["需要API密钥", "按使用量付费"],
                "status": "coming_soon"
            }
        ]
    })


@router.post("/test-connection")
async def test_ai_connection(
    provider: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """测试AI提供商连接"""
    try:
        ai_manager = AIProviderManager(db, current_user.id)

        test_provider = None
        if provider:
            test_provider = AIProvider(provider)

        result = await ai_manager.test_connection(test_provider)

        if result["success"]:
            return success_response(data=result, message="连接测试成功")
        else:
            return error_response(message=result.get("error", "连接测试失败"))

    except Exception as e:
        return error_response(message=f"连接测试失败: {str(e)}")


@router.get("/models")
async def get_available_models(
    provider: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取可用模型列表"""
    try:
        ai_manager = AIProviderManager(db, current_user.id)

        target_provider = None
        if provider:
            target_provider = AIProvider(provider)

        result = await ai_manager.get_available_models(target_provider)

        if result["success"]:
            return success_response(data=result)
        else:
            return error_response(message=result.get("error", "获取模型列表失败"))

    except Exception as e:
        return error_response(message=f"获取模型列表失败: {str(e)}")


@router.post("/ollama/pull-model")
async def pull_ollama_model(
    model_name: str,
    current_user: User = Depends(get_current_user)
):
    """拉取Ollama模型"""
    try:
        ollama_service = await get_ollama_service()

        async with ollama_service as service:
            result = await service.pull_model(model_name)

            if result["success"]:
                return success_response(data=result, message=f"模型 {model_name} 拉取成功")
            else:
                return error_response(message=result.get("error", "拉取模型失败"))

    except Exception as e:
        return error_response(message=f"拉取模型失败: {str(e)}")


@router.get("/ollama/health")
async def check_ollama_health():
    """检查Ollama服务健康状态"""
    try:
        ollama_service = await get_ollama_service()

        async with ollama_service as service:
            result = await service.check_health()

            if result["success"]:
                return success_response(data=result, message="Ollama服务正常")
            else:
                return error_response(message=result.get("error", "Ollama服务异常"))

    except Exception as e:
        return error_response(message=f"健康检查失败: {str(e)}")


@router.post("/generate-reply-v2")
async def generate_ai_reply_v2(
    request: GenerateReplyRequest,
    provider: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """生成AI回复（支持多提供商）"""
    try:
        ai_manager = AIProviderManager(db, current_user.id)

        target_provider = None
        if provider:
            target_provider = AIProvider(provider)

        result = await ai_manager.generate_reply(
            comment_content=request.comment_content,
            context=request.context,
            template_id=request.template_id,
            provider=target_provider
        )

        if result["success"]:
            return success_response(data=result, message="回复生成成功")
        else:
            return error_response(message=result.get("error", "回复生成失败"))

    except Exception as e:
        return error_response(message=f"生成回复失败: {str(e)}")
