import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Button,
  Tabs,
  Table,
  Progress,
  Tag,
  Space,
  Typography,
  Spin,
  Empty,
} from 'antd';
import {
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  TrendingUpOutlined,
  MessageOutlined,
  UserOutlined,
  EyeOutlined,
  HeartOutlined,
  ReloadOutlined,
  DownloadOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const AnalyticsPage = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState('7days');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadAnalyticsData();
  }, [dateRange]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      // 模拟数据加载
    } catch (error) {
      console.error('加载分析数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 模拟数据
  const overviewStats = {
    totalComments: 1234,
    totalReplies: 856,
    replyRate: 69.4,
    avgResponseTime: 2.5,
  };

  const trendData = [
    { date: '01-12', comments: 45, replies: 32, views: 1200 },
    { date: '01-13', comments: 52, replies: 38, views: 1350 },
    { date: '01-14', comments: 38, replies: 28, views: 980 },
    { date: '01-15', comments: 61, replies: 45, views: 1580 },
    { date: '01-16', comments: 49, replies: 35, views: 1220 },
    { date: '01-17', comments: 67, replies: 48, views: 1680 },
    { date: '01-18', comments: 58, replies: 42, views: 1450 },
  ];

  const topNotes = [
    { id: 1, title: '夏日护肤心得分享', comments: 156, replies: 134, replyRate: 85.9 },
    { id: 2, title: '美妆好物推荐', comments: 128, replies: 98, replyRate: 76.6 },
    { id: 3, title: '穿搭灵感分享', comments: 95, replies: 78, replyRate: 82.1 },
    { id: 4, title: '健身日常记录', comments: 87, replies: 65, replyRate: 74.7 },
    { id: 5, title: '美食制作教程', comments: 76, replies: 58, replyRate: 76.3 },
  ];

  const tabItems = [
    {
      key: 'overview',
      label: (
        <span>
          <BarChartOutlined />
          数据概览
        </span>
      ),
      children: (
        <div>
          <Row gutter={24} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总留言数"
                  value={overviewStats.totalComments}
                  prefix={<MessageOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总回复数"
                  value={overviewStats.totalReplies}
                  prefix={<MessageOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="回复率"
                  value={overviewStats.replyRate}
                  suffix="%"
                  precision={1}
                  prefix={<TrendingUpOutlined />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="平均响应时间"
                  value={overviewStats.avgResponseTime}
                  suffix="小时"
                  precision={1}
                  prefix={<TrendingUpOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Card title="留言趋势" extra={<Button icon={<ReloadOutlined />} size="small">刷新</Button>}>
                <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Empty description="图表组件开发中..." />
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="回复率分析" extra={<Button icon={<ReloadOutlined />} size="small">刷新</Button>}>
                <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Empty description="图表组件开发中..." />
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'notes',
      label: (
        <span>
          <LineChartOutlined />
          笔记分析
        </span>
      ),
      children: (
        <Card title="热门笔记排行">
          <Table
            dataSource={topNotes}
            rowKey="id"
            pagination={false}
            columns={[
              {
                title: '笔记标题',
                dataIndex: 'title',
                key: 'title',
                ellipsis: true,
              },
              {
                title: '留言数',
                dataIndex: 'comments',
                key: 'comments',
                width: 100,
                render: (value) => (
                  <Tag color="blue">{value}</Tag>
                ),
              },
              {
                title: '回复数',
                dataIndex: 'replies',
                key: 'replies',
                width: 100,
                render: (value) => (
                  <Tag color="green">{value}</Tag>
                ),
              },
              {
                title: '回复率',
                dataIndex: 'replyRate',
                key: 'replyRate',
                width: 120,
                render: (value) => (
                  <div>
                    <Progress
                      percent={value}
                      size="small"
                      format={(percent) => `${percent}%`}
                    />
                  </div>
                ),
              },
            ]}
          />
        </Card>
      ),
    },
    {
      key: 'users',
      label: (
        <span>
          <UserOutlined />
          用户分析
        </span>
      ),
      children: (
        <Card title="用户互动分析">
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Empty description="用户分析功能开发中..." />
          </div>
        </Card>
      ),
    },
    {
      key: 'performance',
      label: (
        <span>
          <PieChartOutlined />
          性能分析
        </span>
      ),
      children: (
        <Card title="系统性能分析">
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Empty description="性能分析功能开发中..." />
          </div>
        </Card>
      ),
    },
  ];

  return (
    <div className="analytics-page">
      <div className="page-header" style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2}>数据分析</Title>
            <Text type="secondary">深入了解留言互动数据，优化运营策略</Text>
          </div>
          <Space>
            <Select
              value={dateRange}
              onChange={setDateRange}
              style={{ width: 120 }}
            >
              <Option value="7days">近7天</Option>
              <Option value="30days">近30天</Option>
              <Option value="90days">近90天</Option>
            </Select>
            <RangePicker />
            <Button icon={<DownloadOutlined />}>导出报告</Button>
          </Space>
        </div>
      </div>

      <Spin spinning={loading}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
          items={tabItems}
        />
      </Spin>
    </div>
  );
};

export default AnalyticsPage;
