import React, { useState } from 'react';
import { Card, Typography, Form, Input, Button, Select, message, Space, Alert, Row, Col, Divider, Tag } from 'antd';
import { RobotOutlined, SaveOutlined, HomeOutlined, CloudOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;
const { Option } = Select;

const ConfigPage = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  
  const [config, setConfig] = useState({
    provider: 'ollama',
    ollama_base_url: 'http://localhost:11434',
    ollama_model: 'llama2',
    api_key: '',
    default_model: 'gpt-3.5-turbo',
    temperature: 0.7,
    max_tokens: 150,
  });

  const handleSave = async (values) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setConfig({ ...config, ...values });
      message.success('配置保存成功');
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Title level={2}>AI配置</Title>
      <Paragraph type="secondary">
        配置AI回复参数，支持Ollama本地模型和OpenAI云端服务
      </Paragraph>

      <Card title="AI配置" loading={loading} style={{ marginTop: 16 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={config}
        >
          <Alert
            message="AI提供商配置"
            description="选择AI服务提供商。推荐使用Ollama本地部署，免费且保护隐私。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <Form.Item
            label="AI提供商"
            name="provider"
            rules={[{ required: true, message: '请选择AI提供商' }]}
          >
            <Select placeholder="选择AI提供商">
              <Option value="ollama">
                <Space>
                  <HomeOutlined />
                  Ollama (本地部署)
                  <Tag color="green">推荐</Tag>
                </Space>
              </Option>
              <Option value="openai">
                <Space>
                  <CloudOutlined />
                  OpenAI (云端服务)
                </Space>
              </Option>
            </Select>
          </Form.Item>

          {config.provider === 'ollama' && (
            <>
              <Divider>Ollama配置</Divider>
              
              <Form.Item
                label="Ollama服务地址"
                name="ollama_base_url"
              >
                <Input placeholder="http://localhost:11434" />
              </Form.Item>

              <Form.Item
                label="模型名称"
                name="ollama_model"
              >
                <Select placeholder="选择模型">
                  <Option value="llama2">Llama 2</Option>
                  <Option value="llama2:13b">Llama 2 13B</Option>
                  <Option value="codellama">Code Llama</Option>
                  <Option value="mistral">Mistral</Option>
                </Select>
              </Form.Item>
            </>
          )}

          {config.provider === 'openai' && (
            <>
              <Divider>OpenAI配置</Divider>
              
              <Form.Item
                label="API密钥"
                name="api_key"
              >
                <Input.Password placeholder="sk-..." />
              </Form.Item>

              <Form.Item
                label="模型"
                name="default_model"
              >
                <Select placeholder="选择模型">
                  <Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Option>
                  <Option value="gpt-4">GPT-4</Option>
                </Select>
              </Form.Item>
            </>
          )}

          <Divider>回复参数</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="温度参数" name="temperature">
                <Input placeholder="0.7" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="最大令牌数" name="max_tokens">
                <Input placeholder="150" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              保存配置
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ConfigPage;
