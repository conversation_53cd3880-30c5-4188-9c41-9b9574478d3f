import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  InputNumber,
  Switch,
  message,
  Typography,
  Divider,
  Space,
  Alert,
  Tabs,
  Progress,
  Statistic,
  Row,
  Col,
} from 'antd';
import {
  RobotOutlined,
  SettingOutlined,
  DollarOutlined,
  SafetyOutlined,
  SaveOutlined,
  ReloadOutlined,
  TestOutlined,
  WarningOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const ConfigPage = () => {
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [basicForm] = Form.useForm();
  const [config, setConfig] = useState({
    apiKey: '',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 1000,
    enabled: true,
  });

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      // 模拟加载配置
      await new Promise(resolve => setTimeout(resolve, 500));
      basicForm.setFieldsValue(config);
    } catch (error) {
      message.error('加载配置失败');
    }
  };

  const handleSaveBasic = async (values) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setConfig({ ...config, ...values });
      message.success('基础配置保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleTestAPI = async () => {
    setTestLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('API连接测试成功！');
    } catch (error) {
      message.error('API连接测试失败，请检查配置');
    } finally {
      setTestLoading(false);
    }
  };

  const tabItems = [
    {
      key: 'basic',
      label: (
        <span>
          <RobotOutlined />
          基础配置
        </span>
      ),
      children: (
        <Card>
          <Form
            form={basicForm}
            layout="vertical"
            onFinish={handleSaveBasic}
            initialValues={config}
          >
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="API密钥"
                  name="apiKey"
                  rules={[{ required: true, message: '请输入API密钥' }]}
                >
                  <Input.Password placeholder="请输入OpenAI API密钥" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="AI模型"
                  name="model"
                  rules={[{ required: true, message: '请选择AI模型' }]}
                >
                  <Select placeholder="选择AI模型">
                    <Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Option>
                    <Option value="gpt-4">GPT-4</Option>
                    <Option value="gpt-4-turbo">GPT-4 Turbo</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="温度参数"
                  name="temperature"
                  tooltip="控制回复的创造性，0-1之间，值越高越有创造性"
                >
                  <InputNumber
                    min={0}
                    max={1}
                    step={0.1}
                    style={{ width: '100%' }}
                    placeholder="0.7"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="最大令牌数"
                  name="maxTokens"
                  tooltip="单次回复的最大字符数限制"
                >
                  <InputNumber
                    min={100}
                    max={4000}
                    style={{ width: '100%' }}
                    placeholder="1000"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label="启用AI回复"
              name="enabled"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              label="系统提示词"
              name="systemPrompt"
              tooltip="定义AI的回复风格和行为"
            >
              <TextArea
                rows={4}
                placeholder="你是一个友好的小红书博主助手，请用温暖、亲切的语气回复用户留言..."
              />
            </Form.Item>

            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                保存配置
              </Button>
              <Button
                loading={testLoading}
                icon={<TestOutlined />}
                onClick={handleTestAPI}
              >
                测试连接
              </Button>
              <Button icon={<ReloadOutlined />} onClick={loadConfig}>
                重置
              </Button>
            </Space>
          </Form>
        </Card>
      ),
    },
    {
      key: 'advanced',
      label: (
        <span>
          <SettingOutlined />
          高级配置
        </span>
      ),
      children: (
        <Card>
          <Alert
            message="高级配置"
            description="这些设置需要对AI模型有深入了解，请谨慎修改。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Text type="secondary">高级配置功能开发中...</Text>
          </div>
        </Card>
      ),
    },
    {
      key: 'cost',
      label: (
        <span>
          <DollarOutlined />
          成本控制
        </span>
      ),
      children: (
        <Card>
          <Alert
            message="成本控制"
            description="设置API调用限制，控制使用成本。"
            type="warning"
            showIcon
            style={{ marginBottom: 24 }}
          />
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Text type="secondary">成本控制功能开发中...</Text>
          </div>
        </Card>
      ),
    },
    {
      key: 'security',
      label: (
        <span>
          <SafetyOutlined />
          安全设置
        </span>
      ),
      children: (
        <Card>
          <Alert
            message="安全设置"
            description="配置内容过滤和安全检查规则。"
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
          />
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Text type="secondary">安全设置功能开发中...</Text>
          </div>
        </Card>
      ),
    },
  ];

  return (
    <div className="ai-config-page">
      <div className="page-header">
        <Title level={2}>AI配置</Title>
        <Text type="secondary">配置AI回复的参数和行为</Text>
      </div>

      <Card style={{ marginBottom: 24 }}>
        <Row gutter={24}>
          <Col span={6}>
            <Statistic
              title="API调用次数"
              value={1234}
              prefix={<RobotOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="成功率"
              value={98.5}
              suffix="%"
              precision={1}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="本月费用"
              value={45.67}
              prefix="$"
              precision={2}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="剩余额度"
              value={154.33}
              prefix="$"
              precision={2}
            />
          </Col>
        </Row>
      </Card>

      <Tabs defaultActiveKey="basic" type="card" items={tabItems} />
    </div>
  );
};

export default ConfigPage;
